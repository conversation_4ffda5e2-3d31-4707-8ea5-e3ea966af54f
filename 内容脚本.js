// 广告弹窗杀手 - 内容脚本
class AdPopupKiller {
    constructor() {
        this.isEnabled = true;
        this.scanInterval = null;
        this.closedCount = 0;
        
        // 常见的关闭按钮选择器
        this.closeButtonSelectors = [
            // 基于你提供的SVG元素
            'svg[data-testid="beast-core-modal-icon-close"]',
            '.MDL_headerCloseIcon_5-118-0',
            
            // 通用关闭按钮选择器
            '[class*="close"]',
            '[class*="Close"]',
            '[class*="CLOSE"]',
            '[id*="close"]',
            '[id*="Close"]',
            '[data-dismiss="modal"]',
            '[aria-label*="close"]',
            '[aria-label*="Close"]',
            '[aria-label*="关闭"]',
            '[title*="close"]',
            '[title*="Close"]',
            '[title*="关闭"]',
            
            // 常见的弹窗关闭按钮
            '.modal-close',
            '.popup-close',
            '.dialog-close',
            '.overlay-close',
            '.lightbox-close',
            '.fancybox-close',
            
            // X 符号相关
            '[class*="times"]',
            '[class*="cross"]',
            '[class*="cancel"]',
            
            // 特定网站的关闭按钮
            '.layui-layer-close',
            '.layui-layer-close1',
            '.layui-layer-close2',
            '.ui-dialog-titlebar-close',
            '.mfp-close',
            '.fancybox-button--close'
        ];
        
        // 弹窗容器选择器
        this.popupSelectors = [
            '.modal',
            '.popup',
            '.dialog',
            '.overlay',
            '.lightbox',
            '.fancybox-container',
            '[class*="modal"]',
            '[class*="popup"]',
            '[class*="dialog"]',
            '[class*="overlay"]',
            '[role="dialog"]',
            '[role="alertdialog"]'
        ];
        
        this.init();
    }
    
    init() {
        console.log('广告弹窗杀手已启动');
        this.startScanning();
        
        // 监听来自后台脚本的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'toggle') {
                this.toggle();
                sendResponse({enabled: this.isEnabled, closedCount: this.closedCount});
            } else if (request.action === 'getStatus') {
                sendResponse({enabled: this.isEnabled, closedCount: this.closedCount});
            }
        });
    }
    
    startScanning() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
        }
        
        // 每0.5秒扫描一次
        this.scanInterval = setInterval(() => {
            if (this.isEnabled) {
                this.scanAndClosePopups();
            }
        }, 500);
    }
    
    scanAndClosePopups() {
        try {
            // 查找并关闭弹窗
            let closed = false;
            
            // 方法1: 直接查找关闭按钮
            for (const selector of this.closeButtonSelectors) {
                const closeButtons = document.querySelectorAll(selector);
                for (const button of closeButtons) {
                    if (this.isVisibleAndClickable(button)) {
                        this.clickElement(button);
                        closed = true;
                        console.log(`关闭弹窗按钮: ${selector}`);
                    }
                }
            }
            
            // 方法2: 查找弹窗容器中的关闭按钮
            for (const popupSelector of this.popupSelectors) {
                const popups = document.querySelectorAll(popupSelector);
                for (const popup of popups) {
                    if (this.isVisibleAndClickable(popup)) {
                        // 在弹窗内查找关闭按钮
                        const closeButton = this.findCloseButtonInPopup(popup);
                        if (closeButton) {
                            this.clickElement(closeButton);
                            closed = true;
                            console.log(`在弹窗内找到关闭按钮: ${popupSelector}`);
                        }
                    }
                }
            }
            
            // 方法3: 查找包含X符号的元素
            this.findAndCloseXButtons();
            
            if (closed) {
                this.closedCount++;
                this.notifyBackgroundScript();
            }
            
        } catch (error) {
            console.error('扫描弹窗时出错:', error);
        }
    }
    
    findCloseButtonInPopup(popup) {
        // 在弹窗内查找关闭按钮
        for (const selector of this.closeButtonSelectors) {
            const button = popup.querySelector(selector);
            if (button && this.isVisibleAndClickable(button)) {
                return button;
            }
        }
        
        // 查找包含特定文本的按钮
        const textButtons = popup.querySelectorAll('button, a, span, div');
        for (const button of textButtons) {
            const text = button.textContent.trim().toLowerCase();
            if (text === '×' || text === 'x' || text === '关闭' || text === 'close' || 
                text === '取消' || text === 'cancel' || text === '确定' || text === 'ok') {
                if (this.isVisibleAndClickable(button)) {
                    return button;
                }
            }
        }
        
        return null;
    }
    
    findAndCloseXButtons() {
        // 查找包含X符号的元素
        const allElements = document.querySelectorAll('*');
        for (const element of allElements) {
            const text = element.textContent.trim();
            if ((text === '×' || text === 'X' || text === 'x') && 
                element.children.length === 0 && 
                this.isVisibleAndClickable(element)) {
                
                // 检查是否可能是关闭按钮
                const style = window.getComputedStyle(element);
                if (style.cursor === 'pointer' || 
                    element.onclick || 
                    element.getAttribute('onclick') ||
                    element.closest('[role="dialog"]') ||
                    element.closest('.modal') ||
                    element.closest('.popup')) {
                    
                    this.clickElement(element);
                    console.log('关闭X按钮');
                    return true;
                }
            }
        }
        return false;
    }
    
    isVisibleAndClickable(element) {
        if (!element) return false;
        
        const style = window.getComputedStyle(element);
        const rect = element.getBoundingClientRect();
        
        return style.display !== 'none' &&
               style.visibility !== 'hidden' &&
               style.opacity !== '0' &&
               rect.width > 0 &&
               rect.height > 0 &&
               rect.top >= 0 &&
               rect.left >= 0;
    }
    
    clickElement(element) {
        try {
            // 尝试多种点击方式
            if (element.click) {
                element.click();
            }
            
            // 触发鼠标事件
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            element.dispatchEvent(clickEvent);
            
            // 如果是链接，尝试触发
            if (element.tagName === 'A' && element.href) {
                element.click();
            }
            
        } catch (error) {
            console.error('点击元素时出错:', error);
        }
    }
    
    toggle() {
        this.isEnabled = !this.isEnabled;
        console.log(`广告弹窗杀手 ${this.isEnabled ? '已启用' : '已禁用'}`);
        
        if (this.isEnabled) {
            this.startScanning();
        } else {
            if (this.scanInterval) {
                clearInterval(this.scanInterval);
                this.scanInterval = null;
            }
        }
    }
    
    notifyBackgroundScript() {
        try {
            chrome.runtime.sendMessage({
                action: 'popupClosed',
                count: this.closedCount,
                url: window.location.href
            });
        } catch (error) {
            // 忽略错误，可能是在扩展重新加载时发生
        }
    }
}

// 等待DOM加载完成后启动
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new AdPopupKiller();
    });
} else {
    new AdPopupKiller();
}

// 也在页面完全加载后再次启动，以防遗漏
window.addEventListener('load', () => {
    if (!window.adPopupKiller) {
        window.adPopupKiller = new AdPopupKiller();
    }
});
