// 广告弹窗杀手 - 后台脚本
class BackgroundScript {
    constructor() {
        this.totalClosedCount = 0;
        this.isEnabled = true;
        this.init();
    }
    
    init() {
        console.log('广告弹窗杀手后台脚本已启动');
        
        // 监听来自内容脚本的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'popupClosed') {
                this.totalClosedCount = request.count;
                this.updateBadge();
                console.log(`已关闭弹窗: ${request.count} 个，网址: ${request.url}`);
            }
        });
        
        // 监听扩展图标点击
        chrome.action.onClicked.addListener((tab) => {
            this.toggleExtension(tab);
        });
        
        // 初始化徽章
        this.updateBadge();
    }
    
    async toggleExtension(tab) {
        try {
            // 向当前标签页的内容脚本发送切换消息
            const response = await chrome.tabs.sendMessage(tab.id, {action: 'toggle'});
            this.isEnabled = response.enabled;
            this.totalClosedCount = response.closedCount;
            this.updateBadge();
            
            // 显示通知
            const status = this.isEnabled ? '已启用' : '已禁用';
            chrome.notifications.create({
                type: 'basic',
                iconUrl: '图标48.png',
                title: '广告弹窗杀手',
                message: `插件${status}，已关闭 ${this.totalClosedCount} 个弹窗`
            });
            
        } catch (error) {
            console.error('切换扩展状态时出错:', error);
        }
    }
    
    updateBadge() {
        // 更新扩展图标上的徽章
        const badgeText = this.totalClosedCount > 0 ? this.totalClosedCount.toString() : '';
        const badgeColor = this.isEnabled ? '#4CAF50' : '#F44336';
        
        chrome.action.setBadgeText({text: badgeText});
        chrome.action.setBadgeBackgroundColor({color: badgeColor});
        
        // 更新图标标题
        const title = this.isEnabled ? 
            `广告弹窗杀手 (已启用) - 已关闭 ${this.totalClosedCount} 个弹窗` :
            `广告弹窗杀手 (已禁用) - 已关闭 ${this.totalClosedCount} 个弹窗`;
        chrome.action.setTitle({title: title});
    }
}

// 启动后台脚本
new BackgroundScript();
