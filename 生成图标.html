<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>图标生成器</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', sans-serif;
      padding: 20px;
      background: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .icon-preview {
      display: flex;
      gap: 20px;
      margin: 20px 0;
      align-items: center;
    }
    .icon-size {
      text-align: center;
    }
    .icon-size img {
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    button {
      background: #667eea;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover {
      background: #5a6fd8;
    }
    .download-section {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Chrome插件图标生成器</h1>
    <p>这个工具可以将SVG图标转换为Chrome插件所需的不同尺寸PNG图标。</p>
    
    <div class="icon-preview">
      <div class="icon-size">
        <h3>16x16</h3>
        <canvas id="canvas16" width="16" height="16"></canvas>
        <br />
        <button onclick="downloadIcon(16)">下载 16x16</button>
      </div>
      
      <div class="icon-size">
        <h3>48x48</h3>
        <canvas id="canvas48" width="48" height="48"></canvas>
        <br />
        <button onclick="downloadIcon(48)">下载 48x48</button>
      </div>
      
      <div class="icon-size">
        <h3>128x128</h3>
        <canvas id="canvas128" width="128" height="128"></canvas>
        <br />
        <button onclick="downloadIcon(128)">下载 128x128</button>
      </div>
    </div>
    
    <div class="download-section">
      <h3>批量下载</h3>
      <button onclick="downloadAllIcons()">下载所有图标</button>
      <p><small>点击后会依次下载三个不同尺寸的图标文件</small></p>
    </div>
    
    <div style="margin-top: 30px;">
      <h3>使用说明</h3>
      <ol>
        <li>页面加载后会自动生成三种尺寸的图标</li>
        <li>点击对应按钮下载单个图标</li>
        <li>或点击"下载所有图标"批量下载</li>
        <li>将下载的图标重命名为：图标16.png、图标48.png、图标128.png</li>
        <li>放入插件文件夹中即可使用</li>
      </ol>
    </div>
  </div>

  <script>
    // SVG图标数据
    const svgData = `
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 128 128" width="128" height="128">
        <defs>
          <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
          </linearGradient>
        </defs>
        
        <!-- 背景圆形 -->
        <circle cx="64" cy="64" r="60" fill="url(#grad1)" stroke="#fff" stroke-width="4"/>
        
        <!-- 盾牌形状 -->
        <path d="M64 20 L44 30 L44 60 Q44 80 64 100 Q84 80 84 60 L84 30 Z" fill="#fff" opacity="0.9"/>
        
        <!-- X 符号 -->
        <g stroke="#667eea" stroke-width="6" stroke-linecap="round">
          <line x1="50" y1="50" x2="78" y2="78"/>
          <line x1="78" y1="50" x2="50" y2="78"/>
        </g>
        
        <!-- 小圆点装饰 -->
        <circle cx="64" cy="35" r="3" fill="#667eea"/>
        <circle cx="52" cy="85" r="2" fill="#667eea" opacity="0.7"/>
        <circle cx="76" cy="85" r="2" fill="#667eea" opacity="0.7"/>
      </svg>
    `;

    // 生成图标
    function generateIcon(size) {
      const canvas = document.getElementById(`canvas${size}`);
      const ctx = canvas.getContext('2d');
      
      // 创建图片对象
      const img = new Image();
      const svgBlob = new Blob([svgData], {type: 'image/svg+xml'});
      const url = URL.createObjectURL(svgBlob);
      
      img.onload = function() {
        // 清除画布
        ctx.clearRect(0, 0, size, size);
        
        // 绘制图标
        ctx.drawImage(img, 0, 0, size, size);
        
        // 清理URL
        URL.revokeObjectURL(url);
      };
      
      img.src = url;
    }

    // 下载图标
    function downloadIcon(size) {
      const canvas = document.getElementById(`canvas${size}`);
      const link = document.createElement('a');
      link.download = `图标${size}.png`;
      link.href = canvas.toDataURL('image/png');
      link.click();
    }

    // 下载所有图标
    function downloadAllIcons() {
      setTimeout(() => downloadIcon(16), 100);
      setTimeout(() => downloadIcon(48), 200);
      setTimeout(() => downloadIcon(128), 300);
    }

    // 页面加载后生成所有图标
    window.onload = function() {
      setTimeout(() => generateIcon(16), 100);
      setTimeout(() => generateIcon(48), 200);
      setTimeout(() => generateIcon(128), 300);
    };
  </script>
</body>
</html>
