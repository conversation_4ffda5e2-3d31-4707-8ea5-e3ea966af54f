// 广告弹窗杀手 - 弹出页面脚本
class PopupScript {
    constructor() {
        this.isEnabled = true;
        this.closedCount = 0;
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.updateUI();
        this.loadStatus();
    }
    
    bindEvents() {
        // 切换按钮事件
        const toggleBtn = document.getElementById('toggleBtn');
        toggleBtn.addEventListener('click', () => {
            this.toggleExtension();
        });
        
        // 重置按钮事件
        const resetBtn = document.getElementById('resetBtn');
        resetBtn.addEventListener('click', () => {
            this.resetCount();
        });
    }
    
    async loadStatus() {
        try {
            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
            
            // 向内容脚本请求状态
            const response = await chrome.tabs.sendMessage(tab.id, {action: 'getStatus'});
            
            if (response) {
                this.isEnabled = response.enabled;
                this.closedCount = response.closedCount;
                this.updateUI();
            }
        } catch (error) {
            console.error('获取状态失败:', error);
            // 如果无法获取状态，显示默认状态
            this.updateStatusText('无法连接到页面');
        }
    }
    
    async toggleExtension() {
        try {
            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
            
            // 向内容脚本发送切换消息
            const response = await chrome.tabs.sendMessage(tab.id, {action: 'toggle'});
            
            if (response) {
                this.isEnabled = response.enabled;
                this.closedCount = response.closedCount;
                this.updateUI();
                
                // 显示反馈
                this.showFeedback(this.isEnabled ? '已启用' : '已禁用');
            }
        } catch (error) {
            console.error('切换扩展状态失败:', error);
            this.showFeedback('操作失败，请刷新页面后重试');
        }
    }
    
    resetCount() {
        this.closedCount = 0;
        this.updateClosedCount();
        this.showFeedback('计数已重置');
    }
    
    updateUI() {
        this.updateToggleButton();
        this.updateStatusIndicator();
        this.updateClosedCount();
    }
    
    updateToggleButton() {
        const toggleBtn = document.getElementById('toggleBtn');
        const toggleText = document.getElementById('toggleText');
        
        if (this.isEnabled) {
            toggleBtn.classList.remove('disabled');
            toggleText.textContent = '禁用';
        } else {
            toggleBtn.classList.add('disabled');
            toggleText.textContent = '启用';
        }
    }
    
    updateStatusIndicator() {
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');
        
        if (this.isEnabled) {
            statusDot.classList.remove('disabled');
            statusText.textContent = '正在运行';
        } else {
            statusDot.classList.add('disabled');
            statusText.textContent = '已禁用';
        }
    }
    
    updateStatusText(text) {
        const statusText = document.getElementById('statusText');
        statusText.textContent = text;
    }
    
    updateClosedCount() {
        const closedCountElement = document.getElementById('closedCount');
        closedCountElement.textContent = this.closedCount;
    }
    
    showFeedback(message) {
        // 创建临时反馈元素
        const feedback = document.createElement('div');
        feedback.textContent = message;
        feedback.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            pointer-events: none;
        `;
        
        document.body.appendChild(feedback);
        
        // 2秒后移除反馈
        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.parentNode.removeChild(feedback);
            }
        }, 2000);
    }
}

// 等待DOM加载完成后启动
document.addEventListener('DOMContentLoaded', () => {
    new PopupScript();
});
