/* 广告弹窗杀手 - 弹出页面样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  width: 320px;
  min-height: 400px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  border-radius: 8px;
  margin: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px;
  text-align: center;
}

.header h1 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #4CAF50;
  animation: pulse 2s infinite;
}

.status-dot.disabled {
  background: #F44336;
  animation: none;
}

.status-text {
  font-size: 12px;
  opacity: 0.9;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.main {
  flex: 1;
  padding: 16px;
}

.stats-section {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  font-size: 13px;
  color: #666;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.controls-section {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.toggle-btn {
  flex: 1;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  background: #4CAF50;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  background: #45a049;
  transform: translateY(-1px);
}

.toggle-btn.disabled {
  background: #F44336;
}

.toggle-btn.disabled:hover {
  background: #da190b;
}

.reset-btn {
  padding: 10px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

.info-section {
  margin-bottom: 16px;
}

.info-section h3 {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.info-section ul {
  list-style: none;
  padding-left: 0;
}

.info-section li {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  padding-left: 16px;
  position: relative;
}

.info-section li::before {
  content: '•';
  color: #667eea;
  position: absolute;
  left: 0;
}

.supported-elements h3 {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.element-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.footer {
  background: #f8f9fa;
  padding: 12px 16px;
  text-align: center;
  border-top: 1px solid #eee;
}

.footer p {
  font-size: 11px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 350px) {
  body {
    width: 280px;
  }
  
  .container {
    margin: 4px;
  }
  
  .header {
    padding: 12px;
  }
  
  .main {
    padding: 12px;
  }
}
