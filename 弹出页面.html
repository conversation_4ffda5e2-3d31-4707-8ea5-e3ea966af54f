<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>广告弹窗杀手</title>
  <link rel="stylesheet" href="弹出页面样式.css" />
</head>
<body>
  <div class="container">
    <header class="header">
      <h1>广告弹窗杀手</h1>
      <div class="status-indicator" id="statusIndicator">
        <span class="status-dot" id="statusDot"></span>
        <span class="status-text" id="statusText">检查中...</span>
      </div>
    </header>
    
    <main class="main">
      <div class="stats-section">
        <div class="stat-item">
          <span class="stat-label">已关闭弹窗:</span>
          <span class="stat-value" id="closedCount">0</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">扫描间隔:</span>
          <span class="stat-value">0.5秒</span>
        </div>
      </div>
      
      <div class="controls-section">
        <button class="toggle-btn" id="toggleBtn">
          <span id="toggleText">启用</span>
        </button>
        <button class="reset-btn" id="resetBtn">重置计数</button>
      </div>
      
      <div class="info-section">
        <h3>功能说明:</h3>
        <ul>
          <li>每0.5秒自动扫描页面弹窗</li>
          <li>智能识别各种关闭按钮</li>
          <li>支持常见弹窗框架</li>
          <li>点击扩展图标可快速开关</li>
        </ul>
      </div>
      
      <div class="supported-elements">
        <h3>支持的关闭元素:</h3>
        <div class="element-tags">
          <span class="tag">× 符号</span>
          <span class="tag">关闭按钮</span>
          <span class="tag">模态框</span>
          <span class="tag">弹出层</span>
          <span class="tag">对话框</span>
        </div>
      </div>
    </main>
    
    <footer class="footer">
      <p>版本 1.0 | 自动广告拦截</p>
    </footer>
  </div>
  
  <script src="弹出页面脚本.js"></script>
</body>
</html>
