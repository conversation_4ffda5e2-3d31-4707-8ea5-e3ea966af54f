<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>广告弹窗杀手 - 测试页面</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', sans-serif;
      margin: 0;
      padding: 20px;
      background: #f5f5f5;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .test-section {
      margin: 30px 0;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
    }
    
    button {
      background: #667eea;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      margin: 5px;
    }
    
    button:hover {
      background: #5a6fd8;
    }
    
    /* 模态框样式 */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
    }
    
    .modal-content {
      background-color: white;
      margin: 15% auto;
      padding: 20px;
      border-radius: 10px;
      width: 400px;
      position: relative;
    }
    
    .modal-close {
      position: absolute;
      top: 10px;
      right: 15px;
      font-size: 24px;
      cursor: pointer;
      color: #999;
    }
    
    .modal-close:hover {
      color: #333;
    }
    
    /* 弹出层样式 */
    .popup {
      display: none;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      z-index: 1001;
    }
    
    .popup-close {
      position: absolute;
      top: 5px;
      right: 10px;
      font-size: 20px;
      cursor: pointer;
      color: #666;
    }
    
    /* 对话框样式 */
    .dialog {
      display: none;
      position: fixed;
      top: 20%;
      left: 50%;
      transform: translateX(-50%);
      background: white;
      padding: 25px;
      border-radius: 8px;
      box-shadow: 0 2px 15px rgba(0,0,0,0.2);
      z-index: 1002;
      border: 2px solid #667eea;
    }
    
    .dialog .close {
      float: right;
      font-size: 18px;
      cursor: pointer;
      color: #999;
      margin-left: 10px;
    }
    
    /* SVG关闭按钮样式 */
    .svg-close-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 24px;
      height: 24px;
      cursor: pointer;
    }
    
    .svg-close-btn:hover {
      opacity: 0.7;
    }
    
    /* 遮罩层 */
    .overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.3);
      z-index: 999;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>广告弹窗杀手 - 测试页面</h1>
    <p>这个页面用于测试插件的弹窗关闭功能。点击下面的按钮创建不同类型的弹窗，然后观察插件是否能自动关闭它们。</p>
    
    <div class="test-section">
      <h3>测试按钮</h3>
      <button onclick="showModal()">显示模态框</button>
      <button onclick="showPopup()">显示弹出层</button>
      <button onclick="showDialog()">显示对话框</button>
      <button onclick="showSVGModal()">显示SVG关闭按钮弹窗</button>
      <button onclick="showXButton()">显示X按钮弹窗</button>
      <button onclick="showAllPopups()">显示所有弹窗</button>
    </div>
    
    <div class="test-section">
      <h3>测试说明</h3>
      <ul>
        <li>安装并启用"广告弹窗杀手"插件</li>
        <li>点击上面的按钮创建弹窗</li>
        <li>观察弹窗是否在0.5秒内自动关闭</li>
        <li>检查插件图标上的计数是否增加</li>
        <li>可以在浏览器控制台查看插件的日志信息</li>
      </ul>
    </div>
    
    <div class="test-section">
      <h3>支持的关闭元素类型</h3>
      <ul>
        <li>✅ 包含"close"类名的元素</li>
        <li>✅ 包含"×"符号的元素</li>
        <li>✅ SVG关闭图标</li>
        <li>✅ 模态框关闭按钮</li>
        <li>✅ 弹出层关闭按钮</li>
        <li>✅ 对话框关闭按钮</li>
      </ul>
    </div>
  </div>
  
  <!-- 遮罩层 -->
  <div class="overlay" id="overlay"></div>
  
  <!-- 模态框 -->
  <div class="modal" id="modal">
    <div class="modal-content">
      <span class="modal-close" onclick="closeModal()">&times;</span>
      <h3>这是一个模态框</h3>
      <p>插件应该能够自动检测并关闭这个弹窗。</p>
    </div>
  </div>
  
  <!-- 弹出层 -->
  <div class="popup" id="popup">
    <span class="popup-close" onclick="closePopup()">&times;</span>
    <h3>这是一个弹出层</h3>
    <p>插件会寻找关闭按钮并自动点击。</p>
  </div>
  
  <!-- 对话框 -->
  <div class="dialog" id="dialog">
    <span class="close" onclick="closeDialog()">&times;</span>
    <h3>这是一个对话框</h3>
    <p>测试插件的对话框关闭功能。</p>
  </div>
  
  <!-- SVG关闭按钮弹窗 -->
  <div class="modal" id="svgModal">
    <div class="modal-content">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" 
           data-testid="beast-core-modal-icon-close" 
           class="ICN_outerWrapper_5-118-0 MDL_headerCloseIcon_5-118-0 ICN_svgIcon_5-118-0 svg-close-btn"
           onclick="closeSVGModal()">
        <path d="M566.306 512l244.376-244.376c14.997-14.996 14.997-39.309 0-54.305-14.996-14.997-39.309-14.997-54.305 0L512 457.694 267.624 213.318c-14.996-14.997-39.31-14.997-54.306 0-14.996 14.996-14.996 39.309 0 54.305L457.694 512 213.318 756.376c-14.996 14.996-14.996 39.31 0 54.306 14.996 14.996 39.31 14.996 54.306 0L512 566.306l244.376 244.376c14.996 14.996 39.309 14.996 54.305 0 14.997-14.996 14.997-39.31 0-54.306L566.306 512z"></path>
      </svg>
      <h3>SVG关闭按钮弹窗</h3>
      <p>这个弹窗使用了你提供的SVG关闭按钮样式。</p>
    </div>
  </div>
  
  <!-- X按钮弹窗 -->
  <div class="popup" id="xPopup">
    <div style="position: absolute; top: 5px; right: 10px; font-size: 20px; cursor: pointer;" onclick="closeXPopup()">X</div>
    <h3>X按钮弹窗</h3>
    <p>这个弹窗使用简单的X字符作为关闭按钮。</p>
  </div>

  <script>
    // 显示模态框
    function showModal() {
      document.getElementById('overlay').style.display = 'block';
      document.getElementById('modal').style.display = 'block';
    }
    
    // 关闭模态框
    function closeModal() {
      document.getElementById('overlay').style.display = 'none';
      document.getElementById('modal').style.display = 'none';
    }
    
    // 显示弹出层
    function showPopup() {
      document.getElementById('popup').style.display = 'block';
    }
    
    // 关闭弹出层
    function closePopup() {
      document.getElementById('popup').style.display = 'none';
    }
    
    // 显示对话框
    function showDialog() {
      document.getElementById('dialog').style.display = 'block';
    }
    
    // 关闭对话框
    function closeDialog() {
      document.getElementById('dialog').style.display = 'none';
    }
    
    // 显示SVG模态框
    function showSVGModal() {
      document.getElementById('overlay').style.display = 'block';
      document.getElementById('svgModal').style.display = 'block';
    }
    
    // 关闭SVG模态框
    function closeSVGModal() {
      document.getElementById('overlay').style.display = 'none';
      document.getElementById('svgModal').style.display = 'none';
    }
    
    // 显示X按钮弹窗
    function showXButton() {
      document.getElementById('xPopup').style.display = 'block';
    }
    
    // 关闭X按钮弹窗
    function closeXPopup() {
      document.getElementById('xPopup').style.display = 'none';
    }
    
    // 显示所有弹窗
    function showAllPopups() {
      showModal();
      setTimeout(showPopup, 1000);
      setTimeout(showDialog, 2000);
      setTimeout(showSVGModal, 3000);
      setTimeout(showXButton, 4000);
    }
    
    // 点击遮罩层关闭弹窗
    document.getElementById('overlay').onclick = function() {
      closeModal();
      closeSVGModal();
    };
  </script>
</body>
</html>
