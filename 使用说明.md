# 广告弹窗杀手 Chrome 浏览器插件

一个智能的Chrome浏览器插件，能够自动检测并关闭网页上的广告弹窗。

## 功能特点

- ✅ **自动扫描**: 每0.5秒自动扫描页面，及时发现弹窗
- ✅ **智能识别**: 支持多种关闭按钮样式和弹窗框架
- ✅ **实时统计**: 显示已关闭的弹窗数量
- ✅ **一键开关**: 点击扩展图标快速启用/禁用
- ✅ **中文界面**: 完全中文化的用户界面

## 支持的弹窗类型

### 关闭按钮识别
- SVG关闭图标（如你提供的beast-core-modal-icon-close）
- 包含"close"、"Close"、"关闭"等文本的按钮
- X、×符号按钮
- 常见CSS类名：`.modal-close`、`.popup-close`等

### 弹窗容器识别
- 模态框（Modal）
- 弹出层（Popup）
- 对话框（Dialog）
- 遮罩层（Overlay）
- 灯箱效果（Lightbox）

## 安装方法

### 开发者模式安装

1. 打开Chrome浏览器
2. 在地址栏输入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含插件文件的文件夹
6. 插件安装完成，会出现在扩展程序列表中

## 使用方法

### 基本使用
1. 安装插件后，它会自动在所有网页上运行
2. 插件会在扩展程序工具栏显示徽章数字，表示已关闭的弹窗数量
3. 绿色徽章表示插件已启用，红色表示已禁用

### 控制面板
- 点击插件在工具栏的位置打开控制面板
- 查看实时统计信息
- 手动启用/禁用插件
- 重置关闭计数

### 快捷操作
- 直接点击插件可快速开关插件
- 插件会记住每个标签页的状态

## 技术实现

### 核心算法
```javascript
// 扫描频率：每0.5秒
setInterval(() => {
    if (this.isEnabled) {
        this.scanAndClosePopups();
    }
}, 500);
```

### 识别策略
1. **选择器匹配**: 使用预定义的CSS选择器查找关闭按钮
2. **文本内容识别**: 检查元素文本是否包含关闭相关词汇
3. **可见性检测**: 确保元素在页面上可见且可点击
4. **智能点击**: 使用多种方式触发点击事件

### 支持的选择器
```javascript
// 基于你提供的SVG元素
'svg[data-testid="beast-core-modal-icon-close"]'
'.MDL_headerCloseIcon_5-118-0'

// 通用选择器
'[class*="close"]'
'[aria-label*="关闭"]'
'.modal-close'
// ... 更多选择器
```

## 文件结构

```
杀死广告弹窗/
├── manifest.json          # 插件配置文件
├── 内容脚本.js            # 主要功能脚本
├── 后台脚本.js            # 后台服务脚本
├── 弹出页面.html          # 控制面板HTML
├── 弹出页面样式.css       # 控制面板样式
├── 弹出页面脚本.js        # 控制面板脚本
├── 测试页面.html          # 功能测试页面
└── 使用说明.md           # 本文件
```

## 兼容性

- ✅ Chrome 88+
- ✅ Edge 88+
- ✅ 其他基于Chromium的浏览器

## 注意事项

1. **权限说明**: 插件需要访问所有网站以检测弹窗
2. **性能影响**: 0.5秒的扫描间隔对性能影响极小
3. **误关闭**: 极少数情况下可能误关闭正常的对话框
4. **网站兼容**: 某些使用特殊技术的网站可能需要手动处理

## 故障排除

### 插件不工作
1. 检查插件是否已启用
2. 刷新页面重新加载插件
3. 检查浏览器控制台是否有错误信息

### 某些弹窗无法关闭
1. 查看浏览器开发者工具，找到弹窗的HTML结构
2. 在内容脚本中添加对应的选择器
3. 重新加载插件

### 性能问题
1. 可以修改扫描间隔（默认500ms）
2. 在特定网站禁用插件
3. 使用浏览器任务管理器监控资源使用

## 更新日志

### v1.0 (2024-01-01)
- 初始版本发布
- 支持基本的弹窗检测和关闭
- 中文界面和完整的控制面板
- 支持你提供的SVG关闭按钮样式

## 开发者信息

如需自定义或扩展功能，可以修改以下文件：
- `内容脚本.js`: 添加新的选择器或修改检测逻辑
- `弹出页面.html/css/js`: 自定义控制面板界面
- `manifest.json`: 修改权限或添加新功能

## 许可证

本项目采用 MIT 许可证，可自由使用和修改。
